# 封面下载功能说明

## 功能概述

现在漫画下载器会自动下载并保存封面图片到本地，而不是只保存URL链接。

## 主要改进

### 1. 封面下载流程
1. **获取封面URL**：从漫画详情页提取 `data-original` 属性
2. **下载封面图片**：使用浏览器导航到封面URL并下载
3. **保存到本地**：将封面保存为 `cover.{扩展名}` 文件
4. **更新信息**：在JSON中保存本地文件名而不是URL

### 2. 文件结构
```
漫画目录/
├── cover.jpg           # 封面图片（新增）
├── manga-info.json     # 漫画信息
└── 第X章-章节名/
    ├── 1-blob.png
    ├── 2-blob.png
    └── ...
```

### 3. JSON格式变化

**修改前**：
```json
{
  "封面": "https://res.colamanga.com/comic/20339/cover.jpg"
}
```

**修改后**：
```json
{
  "封面": "cover.jpg"
}
```

## 核心方法

### downloadCoverImage()
- 自动检测文件扩展名
- 支持跳过已存在的文件
- 使用浏览器原生下载保证兼容性
- 返回本地文件名

### getMangaInfo()
- 集成封面下载功能
- 自动替换URL为本地路径
- 错误处理和重试机制

## 使用方式

### 基本使用
```javascript
const { MangaContentDownloader } = require('./download-manga-content');

const downloader = new MangaContentDownloader();
await downloader.init();

// 下载漫画（自动包含封面）
await downloader.downloadMangaContent('vj89251', '漫画名称', 1);
```

### 只下载封面
```javascript
const mangaDir = './manga-folder';
const mangaInfo = await downloader.getMangaInfo('vj89251', mangaDir);
// 封面会自动下载到 mangaDir/cover.jpg
```

## 特性

### ✅ 优点
- **本地存储**：封面图片保存在本地，不依赖网络
- **自动检测**：支持多种图片格式（jpg, png, webp等）
- **避免重复**：已存在的封面不会重复下载
- **错误处理**：下载失败时优雅降级
- **统一管理**：封面和漫画内容在同一目录

### 🔧 技术细节
- 使用 `page.goto()` 下载图片，确保兼容性
- 自动解析文件扩展名
- 支持网络超时和重试
- 文件大小显示和验证

## 测试

运行测试脚本验证功能：
```bash
node test-cover-download.js
```

测试会：
1. 下载示例漫画的封面
2. 验证文件是否存在
3. 显示文件大小和路径
4. 保存完整的信息JSON

## 注意事项

1. **网络依赖**：首次下载需要网络连接
2. **存储空间**：封面图片会占用额外存储空间
3. **下载时间**：会增加少量下载时间
4. **权限要求**：需要写入目标目录的权限

## 故障排除

### 常见问题

1. **封面下载失败**
   - 检查网络连接
   - 确认封面URL有效
   - 查看控制台错误信息

2. **文件保存失败**
   - 检查目录权限
   - 确认磁盘空间充足
   - 验证路径正确性

3. **重复下载**
   - 删除现有封面文件强制重新下载
   - 检查文件名是否正确

### 调试方法

启用详细日志查看下载过程：
```javascript
// 在浏览器控制台中查看详细信息
// 所有下载步骤都有相应的日志输出
```
