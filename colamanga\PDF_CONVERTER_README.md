# 漫画转PDF工具

这个工具可以将下载的漫画转换为PDF格式，每个漫画生成一个PDF文件。

## 功能特点

- 🔍 自动扫描漫画目录 (`E:\manga`)
- 📚 将每个漫画的所有章节合并为一个PDF文件
- 📄 按章节和页码顺序排列
- 🖼️ 支持多种图片格式 (PNG, JPG, JPEG, WEBP)
- 📁 输出到专门的PDF目录 (`E:\manga-pdf`)
- ⚡ 自动跳过已存在的PDF文件

## 目录结构

```
E:\manga\                           # 漫画源目录
├── 漫画名称1\
│   ├── 第1章-章节标题\
│   │   ├── 1-blob.png
│   │   ├── 2-blob.png
│   │   └── ...
│   ├── 第2章-章节标题\
│   └── ...
└── 漫画名称2\
    └── ...

E:\manga-pdf\                       # PDF输出目录
├── 漫画名称1.pdf
├── 漫画名称2.pdf
└── ...
```

## 使用方法

### 1. 查看帮助信息

```bash
node colamanga/run-pdf-converter.js --help
```

### 2. 列出所有可用漫画

```bash
node colamanga/run-pdf-converter.js --list
```

### 3. 转换所有漫画

```bash
node colamanga/run-pdf-converter.js
# 然后输入 y 确认转换
```

或者直接使用核心脚本：

```bash
node colamanga/manga-to-pdf.js
```

### 4. 转换指定漫画

```bash
node colamanga/run-pdf-converter.js "漫画名称"
```

或者：

```bash
node colamanga/manga-to-pdf.js "漫画名称"
```

## 示例

```bash
# 查看所有漫画列表
node colamanga/run-pdf-converter.js --list

# 转换名为"进击的巨人"的漫画
node colamanga/run-pdf-converter.js "进击的巨人"

# 转换所有漫画
node colamanga/manga-to-pdf.js
```

## PDF特点

- **格式**: A4纸张大小
- **布局**: 每页一张漫画图片，居中显示
- **章节分隔**: 每个章节前有标题页
- **页码**: 每张图片下方显示页码
- **封面**: 漫画标题作为首页

## 注意事项

1. **依赖要求**: 需要安装 `playwright` 和 `fs-extra`
2. **内存使用**: 转换大型漫画时可能占用较多内存
3. **文件大小**: PDF文件可能比较大，因为包含了所有原始图片
4. **跳过机制**: 如果PDF已存在，会自动跳过转换
5. **错误处理**: 如果某个图片无法读取，会跳过该图片并继续

## 故障排除

### 问题1: 找不到漫画目录
- 确保 `E:\manga` 目录存在
- 检查目录中是否有正确格式的漫画文件夹

### 问题2: 没有找到图片
- 确保章节目录中有图片文件
- 检查图片文件名是否符合格式 `{页码}-xxx.{扩展名}`

### 问题3: PDF生成失败
- 检查输出目录 `E:\manga-pdf` 是否有写入权限
- 确保有足够的磁盘空间

### 问题4: 浏览器启动失败
- 确保已安装 playwright 浏览器: `npx playwright install`

## 技术实现

- **PDF生成**: 使用 Playwright 的 PDF 生成功能
- **图片处理**: 将图片转换为 base64 嵌入HTML
- **文件扫描**: 递归扫描目录结构
- **排序逻辑**: 按章节号和页码数字排序

## 自定义配置

如需修改默认路径，可以编辑 `manga-to-pdf.js` 文件中的配置：

```javascript
constructor() {
    this.mangaDir = 'E:\\manga';      // 漫画源目录
    this.outputDir = 'E:\\manga-pdf'; // PDF输出目录
}
```
