const fs = require('fs-extra');
const path = require('path');
const { chromium } = require('playwright');

class MangaToPdfConverter {
    constructor() {
        this.mangaDir = 'E:\\manga';
        this.outputDir = 'E:\\manga-pdf';
        this.browser = null;
        this.page = null;
    }

    async init() {
        console.log('🚀 启动浏览器...');
        this.browser = await chromium.launch({
            channel: 'chrome',
            headless: true,
            timeout: 300000
        });
        
        this.page = await this.browser.newPage();
        
        // 确保输出目录存在
        await fs.ensureDir(this.outputDir);
        
        console.log('✅ 浏览器启动完成');
    }

    async close() {
        if (this.browser) {
            await this.browser.close();
            console.log('🔒 浏览器已关闭');
        }
    }

    /**
     * 扫描漫画目录，获取所有漫画列表
     */
    async scanMangaDirectory() {
        console.log(`📁 扫描漫画目录: ${this.mangaDir}`);
        
        if (!await fs.pathExists(this.mangaDir)) {
            throw new Error(`漫画目录不存在: ${this.mangaDir}`);
        }

        const items = await fs.readdir(this.mangaDir);
        const mangaList = [];

        for (const item of items) {
            const itemPath = path.join(this.mangaDir, item);
            const stat = await fs.stat(itemPath);
            
            if (stat.isDirectory()) {
                // 检查是否包含章节目录
                const chapters = await this.getChaptersInManga(itemPath);
                if (chapters.length > 0) {
                    mangaList.push({
                        name: item,
                        path: itemPath,
                        chapters: chapters
                    });
                }
            }
        }

        console.log(`📚 找到 ${mangaList.length} 个漫画`);
        return mangaList;
    }

    /**
     * 获取漫画中的所有章节
     */
    async getChaptersInManga(mangaPath) {
        const items = await fs.readdir(mangaPath);
        const chapters = [];

        for (const item of items) {
            const itemPath = path.join(mangaPath, item);
            const stat = await fs.stat(itemPath);
            
            if (stat.isDirectory() && item.startsWith('第') && item.includes('章')) {
                // 提取章节号
                const chapterMatch = item.match(/第(\d+)章/);
                const chapterNumber = chapterMatch ? parseInt(chapterMatch[1]) : 0;
                
                // 检查是否包含图片文件
                const images = await this.getImagesInChapter(itemPath);
                if (images.length > 0) {
                    chapters.push({
                        name: item,
                        path: itemPath,
                        number: chapterNumber,
                        images: images
                    });
                }
            }
        }

        // 按章节号排序
        chapters.sort((a, b) => a.number - b.number);
        return chapters;
    }

    /**
     * 获取章节中的所有图片文件
     */
    async getImagesInChapter(chapterPath) {
        const files = await fs.readdir(chapterPath);
        const imageFiles = files.filter(file => {
            const ext = path.extname(file).toLowerCase();
            return ['.png', '.jpg', '.jpeg', '.webp'].includes(ext);
        });

        // 按页码排序
        const images = imageFiles.map(file => {
            // 提取页码：支持 {页码}-xxx.ext 格式
            const pageMatch = file.match(/^(\d+)-/);
            const pageNumber = pageMatch ? parseInt(pageMatch[1]) : 0;
            
            return {
                filename: file,
                path: path.join(chapterPath, file),
                page: pageNumber
            };
        }).filter(img => img.page > 0);

        // 按页码排序
        images.sort((a, b) => a.page - b.page);
        return images;
    }

    /**
     * 将单个漫画转换为PDF
     */
    async convertMangaToPdf(manga) {
        console.log(`📖 开始转换漫画: ${manga.name}`);
        
        const pdfFileName = this.sanitizeFileName(manga.name) + '.pdf';
        const pdfPath = path.join(this.outputDir, pdfFileName);
        
        // 检查PDF是否已存在
        if (await fs.pathExists(pdfPath)) {
            console.log(`⏭️ PDF已存在，跳过: ${pdfFileName}`);
            return { success: true, skipped: true, path: pdfPath };
        }

        try {
            // 创建HTML内容
            const htmlContent = await this.createHtmlForManga(manga);
            
            // 设置页面内容
            await this.page.setContent(htmlContent, { 
                waitUntil: 'networkidle',
                timeout: 60000 
            });

            console.log(`📄 生成PDF: ${pdfFileName}`);
            
            // 生成PDF
            await this.page.pdf({
                path: pdfPath,
                format: 'A4',
                printBackground: true,
                margin: {
                    top: '10mm',
                    bottom: '10mm',
                    left: '10mm',
                    right: '10mm'
                }
            });

            console.log(`✅ PDF生成成功: ${pdfFileName}`);
            return { success: true, skipped: false, path: pdfPath };
            
        } catch (error) {
            console.error(`❌ 转换失败: ${manga.name} - ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 为漫画创建HTML内容
     */
    async createHtmlForManga(manga) {
        let htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>${manga.name}</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .manga-title {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 30px;
            page-break-after: always;
        }
        .chapter-title {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin: 20px 0;
            page-break-before: always;
        }
        .manga-page {
            text-align: center;
            margin: 10px 0;
            page-break-inside: avoid;
        }
        .manga-page img {
            max-width: 100%;
            max-height: 90vh;
            object-fit: contain;
        }
        .page-number {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="manga-title">${manga.name}</div>
`;

        // 添加每个章节的内容
        for (const chapter of manga.chapters) {
            htmlContent += `    <div class="chapter-title">${chapter.name}</div>\n`;
            
            // 添加章节中的每张图片
            for (const image of chapter.images) {
                // 将图片转换为base64
                const imageBase64 = await this.imageToBase64(image.path);
                if (imageBase64) {
                    htmlContent += `    <div class="manga-page">
        <img src="data:image/png;base64,${imageBase64}" alt="第${image.page}页" />
        <div class="page-number">第${image.page}页</div>
    </div>\n`;
                }
            }
        }

        htmlContent += `</body>
</html>`;

        return htmlContent;
    }

    /**
     * 将图片转换为base64
     */
    async imageToBase64(imagePath) {
        try {
            const imageBuffer = await fs.readFile(imagePath);
            return imageBuffer.toString('base64');
        } catch (error) {
            console.error(`❌ 读取图片失败: ${imagePath} - ${error.message}`);
            return null;
        }
    }

    /**
     * 清理文件名
     */
    sanitizeFileName(fileName) {
        return fileName.replace(/[<>:"/\\|?*：？]/g, '_').trim();
    }

    /**
     * 转换所有漫画
     */
    async convertAllMangas() {
        console.log('🔄 开始转换所有漫画为PDF...');
        
        const mangaList = await this.scanMangaDirectory();
        
        if (mangaList.length === 0) {
            console.log('⚠️ 没有找到任何漫画');
            return;
        }

        let successCount = 0;
        let skippedCount = 0;
        let failedCount = 0;

        for (let i = 0; i < mangaList.length; i++) {
            const manga = mangaList[i];
            console.log(`\n📚 [${i + 1}/${mangaList.length}] 处理漫画: ${manga.name}`);
            console.log(`   章节数: ${manga.chapters.length}`);
            
            const result = await this.convertMangaToPdf(manga);
            
            if (result.success) {
                if (result.skipped) {
                    skippedCount++;
                } else {
                    successCount++;
                }
            } else {
                failedCount++;
            }
        }

        console.log('\n📊 转换完成统计:');
        console.log(`   ✅ 成功: ${successCount}`);
        console.log(`   ⏭️ 跳过: ${skippedCount}`);
        console.log(`   ❌ 失败: ${failedCount}`);
        console.log(`   📁 输出目录: ${this.outputDir}`);
    }

    /**
     * 转换指定的漫画
     */
    async convertSpecificManga(mangaName) {
        console.log(`🔍 查找漫画: ${mangaName}`);
        
        const mangaList = await this.scanMangaDirectory();
        const manga = mangaList.find(m => m.name === mangaName || m.name.includes(mangaName));
        
        if (!manga) {
            console.log(`❌ 未找到漫画: ${mangaName}`);
            console.log('📚 可用的漫画:');
            mangaList.forEach((m, i) => {
                console.log(`   ${i + 1}. ${m.name} (${m.chapters.length}章)`);
            });
            return;
        }

        console.log(`📖 找到漫画: ${manga.name} (${manga.chapters.length}章)`);
        const result = await this.convertMangaToPdf(manga);
        
        if (result.success) {
            if (result.skipped) {
                console.log(`⏭️ PDF已存在: ${result.path}`);
            } else {
                console.log(`✅ PDF生成成功: ${result.path}`);
            }
        } else {
            console.log(`❌ 转换失败: ${result.error}`);
        }
    }
}

module.exports = MangaToPdfConverter;

// 如果直接运行此文件
if (require.main === module) {
    async function main() {
        const converter = new MangaToPdfConverter();
        
        try {
            await converter.init();
            
            // 检查命令行参数
            const args = process.argv.slice(2);
            if (args.length > 0) {
                // 转换指定漫画
                await converter.convertSpecificManga(args[0]);
            } else {
                // 转换所有漫画
                await converter.convertAllMangas();
            }
            
        } catch (error) {
            console.error('❌ 转换过程中出错:', error);
        } finally {
            await converter.close();
        }
    }
    
    main();
}
