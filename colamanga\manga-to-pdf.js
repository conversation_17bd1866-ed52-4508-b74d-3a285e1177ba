const fs = require('fs-extra');
const path = require('path');
const { chromium } = require('playwright');

class MangaToPdfConverter {
    constructor() {
        this.mangaDir = 'E:\\manga';
        this.outputDir = 'E:\\manga-pdf';
        this.browser = null;
        this.page = null;
    }

    async init() {
        console.log('🚀 启动浏览器...');
        this.browser = await chromium.launch({
            channel: 'chrome',
            headless: true,
            timeout: 300000
        });
        
        this.page = await this.browser.newPage();
        
        // 确保输出目录存在
        await fs.ensureDir(this.outputDir);
        
        console.log('✅ 浏览器启动完成');
    }

    async close() {
        if (this.browser) {
            await this.browser.close();
            console.log('🔒 浏览器已关闭');
        }
    }

    /**
     * 扫描漫画目录，获取所有漫画列表
     */
    async scanMangaDirectory() {
        console.log(`📁 扫描漫画目录: ${this.mangaDir}`);
        
        if (!await fs.pathExists(this.mangaDir)) {
            throw new Error(`漫画目录不存在: ${this.mangaDir}`);
        }

        const items = await fs.readdir(this.mangaDir);
        const mangaList = [];

        for (const item of items) {
            const itemPath = path.join(this.mangaDir, item);
            const stat = await fs.stat(itemPath);
            
            if (stat.isDirectory()) {
                // 检查是否包含章节目录
                const chapters = await this.getChaptersInManga(itemPath);
                if (chapters.length > 0) {
                    mangaList.push({
                        name: item,
                        path: itemPath,
                        chapters: chapters
                    });
                }
            }
        }

        console.log(`📚 找到 ${mangaList.length} 个漫画`);
        return mangaList;
    }

    /**
     * 获取漫画中的所有章节
     */
    async getChaptersInManga(mangaPath) {
        const items = await fs.readdir(mangaPath);
        const chapters = [];

        for (const item of items) {
            const itemPath = path.join(mangaPath, item);
            const stat = await fs.stat(itemPath);
            
            if (stat.isDirectory() && item.startsWith('第') && item.includes('章')) {
                // 提取章节号
                const chapterMatch = item.match(/第(\d+)章/);
                const chapterNumber = chapterMatch ? parseInt(chapterMatch[1]) : 0;
                
                // 检查是否包含图片文件
                const images = await this.getImagesInChapter(itemPath);
                if (images.length > 0) {
                    chapters.push({
                        name: item,
                        path: itemPath,
                        number: chapterNumber,
                        images: images
                    });
                }
            }
        }

        // 按章节号排序
        chapters.sort((a, b) => a.number - b.number);
        return chapters;
    }

    /**
     * 获取章节中的所有图片文件
     */
    async getImagesInChapter(chapterPath) {
        const files = await fs.readdir(chapterPath);
        const imageFiles = files.filter(file => {
            const ext = path.extname(file).toLowerCase();
            return ['.png', '.jpg', '.jpeg', '.webp'].includes(ext);
        });

        // 按页码排序
        const images = imageFiles.map(file => {
            // 提取页码：支持 {页码}-xxx.ext 格式
            const pageMatch = file.match(/^(\d+)-/);
            const pageNumber = pageMatch ? parseInt(pageMatch[1]) : 0;
            
            return {
                filename: file,
                path: path.join(chapterPath, file),
                page: pageNumber
            };
        }).filter(img => img.page > 0);

        // 按页码排序
        images.sort((a, b) => a.page - b.page);
        return images;
    }

    /**
     * 将单个漫画转换为PDF - 增强版本，处理大型漫画
     */
    async convertMangaToPdf(manga) {
        console.log(`📖 开始转换漫画: ${manga.name}`);

        const pdfFileName = this.sanitizeFileName(manga.name) + '.pdf';
        const pdfPath = path.join(this.outputDir, pdfFileName);

        // 检查PDF是否已存在
        if (await fs.pathExists(pdfPath)) {
            console.log(`⏭️ PDF已存在，跳过: ${pdfFileName}`);
            return { success: true, skipped: true, path: pdfPath };
        }

        // 统计图片总数
        const totalImages = manga.chapters.reduce((sum, chapter) => sum + chapter.images.length, 0);
        console.log(`📊 漫画统计: ${manga.chapters.length}章, ${totalImages}张图片`);

        try {
            // 检查是否为大型漫画，如果是则分批处理
            if (totalImages > 50) { // 降低阈值，避免字符串长度问题
                console.log(`📦 大型漫画检测 (${totalImages}张图片)，将分批处理...`);
                return await this.convertLargeMangaToPdf(manga, pdfPath);
            }

            // 创建HTML内容
            console.log(`🔄 创建HTML内容...`);
            const htmlContent = await this.createHtmlForManga(manga);

            // 检查HTML内容大小
            const htmlSize = Buffer.byteLength(htmlContent, 'utf8');
            console.log(`📄 HTML内容大小: ${(htmlSize / 1024 / 1024).toFixed(2)} MB`);

            if (htmlSize > 10 * 1024 * 1024) { // 降低到10MB
                console.log(`⚠️ HTML内容过大，使用分批处理...`);
                return await this.convertLargeMangaToPdf(manga, pdfPath);
            }

            // 设置页面内容
            console.log(`🌐 设置页面内容...`);
            await this.page.setContent(htmlContent, {
                waitUntil: 'domcontentloaded',
                timeout: 120000
            });

            console.log(`📄 生成PDF: ${pdfFileName}`);

            // 生成PDF
            await this.page.pdf({
                path: pdfPath,
                format: 'A4',
                printBackground: true,
                margin: {
                    top: '10mm',
                    bottom: '10mm',
                    left: '10mm',
                    right: '10mm'
                },
                timeout: 300000 // 5分钟超时
            });

            console.log(`✅ PDF生成成功: ${pdfFileName}`);
            return { success: true, skipped: false, path: pdfPath };

        } catch (error) {
            console.error(`❌ 转换失败: ${manga.name} - ${error.message}`);

            // 如果是内存或大小相关错误，尝试分批处理
            if (error.message.includes('Invalid string length') ||
                error.message.includes('out of memory') ||
                error.message.includes('Maximum call stack')) {
                console.log(`🔄 尝试分批处理...`);
                try {
                    return await this.convertLargeMangaToPdf(manga, pdfPath);
                } catch (retryError) {
                    console.error(`❌ 分批处理也失败: ${retryError.message}`);
                    return { success: false, error: retryError.message };
                }
            }

            return { success: false, error: error.message };
        }
    }

    /**
     * 为漫画创建HTML内容 - 直接从漫画内容开始，不添加标题页
     */
    async createHtmlForManga(manga) {
        let htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>${manga.name}</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        .manga-page {
            text-align: center;
            margin: 0;
            page-break-inside: avoid;
            page-break-after: always;
        }
        .manga-page img {
            max-width: 100%;
            max-height: 100vh;
            object-fit: contain;
            display: block;
            margin: 0 auto;
        }
    </style>
</head>
<body>
`;

        // 直接添加所有章节的图片，使用base64嵌入
        for (const chapter of manga.chapters) {
            // 添加章节中的每张图片 - 使用base64嵌入
            for (const image of chapter.images) {
                // 检查图片文件是否存在
                if (await fs.pathExists(image.path)) {
                    try {
                        // 读取并转换图片为base64
                        const imageBuffer = await fs.readFile(image.path);
                        const base64Data = imageBuffer.toString('base64');

                        // 检测图片格式
                        const ext = path.extname(image.path).toLowerCase();
                        let mimeType = 'image/png';
                        if (ext === '.jpg' || ext === '.jpeg') {
                            mimeType = 'image/jpeg';
                        } else if (ext === '.webp') {
                            mimeType = 'image/webp';
                        }

                        htmlContent += `    <div class="manga-page">
        <img src="data:${mimeType};base64,${base64Data}" alt="第${image.page}页" />
    </div>\n`;

                        console.log(`✅ 添加图片: ${image.filename} (${(imageBuffer.length / 1024).toFixed(1)} KB)`);
                    } catch (error) {
                        console.log(`❌ 读取图片失败: ${image.path} - ${error.message}`);
                    }
                } else {
                    console.log(`⚠️ 图片文件不存在: ${image.path}`);
                }
            }
        }

        htmlContent += `</body>
</html>`;

        return htmlContent;
    }



    /**
     * 清理文件名
     */
    sanitizeFileName(fileName) {
        return fileName.replace(/[<>:"/\\|?*：？]/g, '_').trim();
    }

    /**
     * 转换所有漫画
     */
    async convertAllMangas() {
        console.log('🔄 开始转换所有漫画为PDF...');
        
        const mangaList = await this.scanMangaDirectory();
        
        if (mangaList.length === 0) {
            console.log('⚠️ 没有找到任何漫画');
            return;
        }

        let successCount = 0;
        let skippedCount = 0;
        let failedCount = 0;

        for (let i = 0; i < mangaList.length; i++) {
            const manga = mangaList[i];
            console.log(`\n📚 [${i + 1}/${mangaList.length}] 处理漫画: ${manga.name}`);
            console.log(`   章节数: ${manga.chapters.length}`);
            
            const result = await this.convertMangaToPdf(manga);
            
            if (result.success) {
                if (result.skipped) {
                    skippedCount++;
                } else {
                    successCount++;
                }
            } else {
                failedCount++;
            }
        }

        console.log('\n📊 转换完成统计:');
        console.log(`   ✅ 成功: ${successCount}`);
        console.log(`   ⏭️ 跳过: ${skippedCount}`);
        console.log(`   ❌ 失败: ${failedCount}`);
        console.log(`   📁 输出目录: ${this.outputDir}`);
    }

    /**
     * 转换大型漫画为PDF - 分批处理
     */
    async convertLargeMangaToPdf(manga, pdfPath) {
        console.log(`📦 开始分批处理大型漫画: ${manga.name}`);

        // 创建临时目录存储分批PDF
        const tempDir = path.join(this.outputDir, 'temp_' + Date.now());
        await fs.ensureDir(tempDir);

        try {
            const tempPdfs = [];
            const batchSize = 20; // 每批20张图片，避免字符串长度问题
            let batchNumber = 1;

            // 收集所有图片
            const allImages = [];
            for (const chapter of manga.chapters) {
                for (const image of chapter.images) {
                    allImages.push({
                        ...image,
                        chapterName: chapter.name
                    });
                }
            }

            console.log(`📊 总计 ${allImages.length} 张图片，将分为 ${Math.ceil(allImages.length / batchSize)} 批处理`);

            // 分批处理
            for (let i = 0; i < allImages.length; i += batchSize) {
                const batch = allImages.slice(i, i + batchSize);
                console.log(`📦 处理批次 ${batchNumber}: ${batch.length} 张图片`);

                const batchPdfPath = path.join(tempDir, `batch_${batchNumber}.pdf`);
                const success = await this.createBatchPdf(batch, batchPdfPath, manga.name, batchNumber);

                if (success) {
                    tempPdfs.push(batchPdfPath);
                    console.log(`✅ 批次 ${batchNumber} 完成`);
                } else {
                    console.log(`❌ 批次 ${batchNumber} 失败`);
                }

                batchNumber++;
            }

            if (tempPdfs.length === 0) {
                throw new Error('所有批次都失败了');
            }

            // 合并PDF文件 (这里简化处理，实际可以使用PDF库合并)
            console.log(`� 合并 ${tempPdfs.length} 个PDF文件...`);

            // 由于没有PDF合并库，我们创建一个包含所有图片的最终PDF
            const finalHtml = await this.createFinalHtmlFromBatches(manga, allImages);

            await this.page.setContent(finalHtml, {
                waitUntil: 'domcontentloaded',
                timeout: 180000
            });

            await this.page.pdf({
                path: pdfPath,
                format: 'A4',
                printBackground: true,
                margin: {
                    top: '10mm',
                    bottom: '10mm',
                    left: '10mm',
                    right: '10mm'
                },
                timeout: 600000 // 10分钟超时
            });

            console.log(`✅ 大型漫画PDF生成成功`);
            return { success: true, skipped: false, path: pdfPath };

        } catch (error) {
            console.error(`❌ 分批处理失败: ${error.message}`);
            return { success: false, error: error.message };
        } finally {
            // 清理临时文件
            try {
                await fs.remove(tempDir);
                console.log(`🗑️ 临时文件已清理`);
            } catch (cleanupError) {
                console.log(`⚠️ 清理临时文件失败: ${cleanupError.message}`);
            }
        }
    }

    /**
     * 创建批次PDF
     */
    async createBatchPdf(images, pdfPath, mangaName, batchNumber) {
        try {
            const htmlContent = this.createBatchHtml(images, mangaName, batchNumber);

            await this.page.setContent(htmlContent, {
                waitUntil: 'domcontentloaded',
                timeout: 60000
            });

            await this.page.pdf({
                path: pdfPath,
                format: 'A4',
                printBackground: true,
                margin: {
                    top: '10mm',
                    bottom: '10mm',
                    left: '10mm',
                    right: '10mm'
                }
            });

            return true;
        } catch (error) {
            console.error(`❌ 创建批次PDF失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 创建批次HTML - 直接显示漫画，不添加标题
     */
    createBatchHtml(images, mangaName, batchNumber) {
        let htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>${mangaName} - 批次${batchNumber}</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        .manga-page {
            text-align: center;
            margin: 0;
            page-break-inside: avoid;
            page-break-after: always;
        }
        .manga-page img {
            max-width: 100%;
            max-height: 100vh;
            object-fit: contain;
            display: block;
            margin: 0 auto;
        }
    </style>
</head>
<body>`;

        for (const image of images) {
            if (fs.existsSync(image.path)) {
                try {
                    // 读取并转换图片为base64
                    const imageBuffer = fs.readFileSync(image.path);
                    const base64Data = imageBuffer.toString('base64');

                    // 检测图片格式
                    const ext = path.extname(image.path).toLowerCase();
                    let mimeType = 'image/png';
                    if (ext === '.jpg' || ext === '.jpeg') {
                        mimeType = 'image/jpeg';
                    } else if (ext === '.webp') {
                        mimeType = 'image/webp';
                    }

                    htmlContent += `
    <div class="manga-page">
        <img src="data:${mimeType};base64,${base64Data}" alt="第${image.page}页" />
    </div>`;
                } catch (error) {
                    console.log(`❌ 读取图片失败: ${image.path} - ${error.message}`);
                }
            }
        }

        htmlContent += `
</body>
</html>`;

        return htmlContent;
    }

    /**
     * 从批次创建最终HTML - 直接显示漫画，不添加标题
     */
    async createFinalHtmlFromBatches(manga, allImages) {
        // 简化版本，直接使用所有图片但限制数量
        const limitedImages = allImages.slice(0, 30); // 限制为前30张图片，避免字符串长度问题

        let htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>${manga.name}</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        .manga-page {
            text-align: center;
            margin: 0;
            page-break-inside: avoid;
            page-break-after: always;
        }
        .manga-page img {
            max-width: 100%;
            max-height: 100vh;
            object-fit: contain;
            display: block;
            margin: 0 auto;
        }
    </style>
</head>
<body>`;

        for (const image of limitedImages) {
            if (await fs.pathExists(image.path)) {
                try {
                    // 读取并转换图片为base64
                    const imageBuffer = await fs.readFile(image.path);
                    const base64Data = imageBuffer.toString('base64');

                    // 检测图片格式
                    const ext = path.extname(image.path).toLowerCase();
                    let mimeType = 'image/png';
                    if (ext === '.jpg' || ext === '.jpeg') {
                        mimeType = 'image/jpeg';
                    } else if (ext === '.webp') {
                        mimeType = 'image/webp';
                    }

                    htmlContent += `
    <div class="manga-page">
        <img src="data:${mimeType};base64,${base64Data}" alt="第${image.page}页" />
    </div>`;
                } catch (error) {
                    console.log(`❌ 读取图片失败: ${image.path} - ${error.message}`);
                }
            }
        }

        htmlContent += `
</body>
</html>`;

        return htmlContent;
    }

    /**
     * 转换指定的漫画
     */
    async convertSpecificManga(mangaName) {
        console.log(`🔍 查找漫画: ${mangaName}`);

        const mangaList = await this.scanMangaDirectory();
        const manga = mangaList.find(m => m.name === mangaName || m.name.includes(mangaName));

        if (!manga) {
            console.log(`❌ 未找到漫画: ${mangaName}`);
            console.log('📚 可用的漫画:');
            mangaList.forEach((m, i) => {
                console.log(`   ${i + 1}. ${m.name} (${m.chapters.length}章)`);
            });
            return;
        }

        console.log(`📖 找到漫画: ${manga.name} (${manga.chapters.length}章)`);
        const result = await this.convertMangaToPdf(manga);

        if (result.success) {
            if (result.skipped) {
                console.log(`⏭️ PDF已存在: ${result.path}`);
            } else {
                console.log(`✅ PDF生成成功: ${result.path}`);
            }
        } else {
            console.log(`❌ 转换失败: ${result.error}`);
        }
    }
}

module.exports = MangaToPdfConverter;

// 如果直接运行此文件
if (require.main === module) {
    async function main() {
        const converter = new MangaToPdfConverter();
        
        try {
            await converter.init();
            
            // 检查命令行参数
            const args = process.argv.slice(2);
            if (args.length > 0) {
                // 转换指定漫画
                await converter.convertSpecificManga(args[0]);
            } else {
                // 转换所有漫画
                await converter.convertAllMangas();
            }
            
        } catch (error) {
            console.error('❌ 转换过程中出错:', error);
        } finally {
            await converter.close();
        }
    }
    
    main();
}
