const { MangaContentDownloader } = require('./download-manga-content');

async function testCoverDownload() {
    console.log('🧪 测试封面下载修改...');
    
    try {
        // 检查语法
        console.log('✅ 文件语法检查通过');
        
        // 创建下载器实例
        const downloader = new MangaContentDownloader();
        console.log('✅ 下载器实例创建成功');
        
        // 检查方法是否存在
        if (typeof downloader.downloadCoverImage === 'function') {
            console.log('✅ downloadCoverImage 方法存在');
        } else {
            console.log('❌ downloadCoverImage 方法不存在');
        }
        
        console.log('🎯 封面下载修改验证完成！');
        console.log('📝 修改内容:');
        console.log('   - 移除了页面导航方式 (page.goto)');
        console.log('   - 使用浏览器内 fetch API');
        console.log('   - 参考漫画图片保存方式');
        console.log('   - 保持页面状态不变');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
    }
}

testCoverDownload();
