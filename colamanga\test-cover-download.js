const { MangaContentDownloader } = require('./download-manga-content');
const fs = require('fs-extra');
const path = require('path');

async function testCoverDownload() {
    console.log('🧪 测试封面下载功能...');
    
    const downloader = new MangaContentDownloader();
    
    try {
        console.log('📋 初始化下载器...');
        await downloader.init();
        
        // 创建测试目录
        const testDir = './test-cover';
        await fs.ensureDir(testDir);
        
        console.log('🔍 测试封面下载...');
        // 使用示例漫画ID
        const mangaId = 'vj89251';
        const mangaInfo = await downloader.getMangaInfo(mangaId, testDir);
        
        if (mangaInfo) {
            console.log('✅ 成功获取漫画信息:');
            console.log(JSON.stringify(mangaInfo, null, 2));
            
            // 检查封面是否下载成功
            if (mangaInfo['封面']) {
                const coverPath = path.join(testDir, mangaInfo['封面']);
                const coverExists = await fs.pathExists(coverPath);
                
                if (coverExists) {
                    const stats = await fs.stat(coverPath);
                    console.log(`✅ 封面下载成功:`);
                    console.log(`   - 文件路径: ${mangaInfo['封面']}`);
                    console.log(`   - 文件大小: ${(stats.size / 1024).toFixed(1)} KB`);
                    console.log(`   - 完整路径: ${coverPath}`);
                } else {
                    console.log('❌ 封面文件不存在');
                }
            } else {
                console.log('⚠️ 未找到封面信息');
            }
            
            // 保存信息文件
            const infoPath = path.join(testDir, 'manga-info.json');
            await fs.writeFile(infoPath, JSON.stringify(mangaInfo, null, 2), 'utf8');
            console.log(`📄 信息文件已保存: ${infoPath}`);
            
        } else {
            console.log('❌ 未能获取漫画信息');
        }
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error('错误详情:', error.stack);
    } finally {
        if (downloader.page) {
            await downloader.page.close();
        }
        console.log('🔚 测试完成');
    }
}

// 运行测试
if (require.main === module) {
    testCoverDownload().catch(console.error);
}

module.exports = { testCoverDownload };
