const MangaDownloader = require('./download-manga-content');
const path = require('path');
const fs = require('fs-extra');

/**
 * 测试封面下载功能
 */
async function testCoverDownload() {
    const downloader = new MangaDownloader();
    
    try {
        console.log('🚀 启动测试封面下载功能...');
        await downloader.init();
        
        // 测试漫画ID列表
        const testMangaIds = [
            '1001', // 可以替换为实际的漫画ID
            '1002',
            '1003'
        ];
        
        console.log(`📚 将测试 ${testMangaIds.length} 个漫画的封面下载`);
        
        for (const mangaId of testMangaIds) {
            console.log(`\n📖 测试漫画ID: ${mangaId}`);
            
            try {
                // 创建测试目录
                const testDir = path.join('./test-covers', `manga-${mangaId}`);
                await fs.ensureDir(testDir);
                
                // 获取漫画信息（包括封面下载）
                const mangaInfo = await downloader.getMangaInfo(mangaId, testDir);
                
                if (mangaInfo) {
                    console.log(`✅ 漫画信息获取成功:`);
                    console.log(`   标题: ${mangaInfo['标题'] || '未知'}`);
                    console.log(`   作者: ${mangaInfo['作者'] || '未知'}`);
                    console.log(`   状态: ${mangaInfo['状态'] || '未知'}`);
                    console.log(`   封面: ${mangaInfo['封面'] || '未下载'}`);
                    
                    // 保存漫画信息
                    const infoPath = path.join(testDir, 'manga-info.json');
                    await fs.writeFile(infoPath, JSON.stringify(mangaInfo, null, 2), 'utf8');
                    console.log(`💾 漫画信息已保存: ${infoPath}`);
                    
                    // 检查封面文件是否存在
                    if (mangaInfo['封面']) {
                        const coverPath = path.join(testDir, mangaInfo['封面']);
                        if (await fs.pathExists(coverPath)) {
                            const stats = await fs.stat(coverPath);
                            console.log(`📁 封面文件大小: ${(stats.size / 1024).toFixed(1)} KB`);
                        }
                    }
                } else {
                    console.log(`❌ 漫画ID ${mangaId} 信息获取失败`);
                }
                
                // 等待一下避免请求过快
                await new Promise(resolve => setTimeout(resolve, 2000));
                
            } catch (error) {
                console.log(`❌ 测试漫画ID ${mangaId} 时出错: ${error.message}`);
            }
        }
        
        console.log('\n📊 测试完成！');
        console.log('📁 测试结果保存在 ./test-covers/ 目录中');
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
    } finally {
        await downloader.close();
    }
}

/**
 * 测试单个漫画的封面下载
 */
async function testSingleCover(mangaId) {
    const downloader = new MangaDownloader();
    
    try {
        console.log(`🚀 测试单个漫画封面下载: ${mangaId}`);
        await downloader.init();
        
        const testDir = path.join('./test-covers', `single-manga-${mangaId}`);
        await fs.ensureDir(testDir);
        
        const mangaInfo = await downloader.getMangaInfo(mangaId, testDir);
        
        if (mangaInfo) {
            console.log('✅ 封面下载测试成功!');
            console.log('📋 漫画信息:', JSON.stringify(mangaInfo, null, 2));
        } else {
            console.log('❌ 封面下载测试失败!');
        }
        
    } catch (error) {
        console.error('❌ 测试出错:', error);
    } finally {
        await downloader.close();
    }
}

// 检查命令行参数
const args = process.argv.slice(2);

if (args.length > 0) {
    // 测试单个漫画
    const mangaId = args[0];
    testSingleCover(mangaId);
} else {
    // 运行完整测试
    testCoverDownload();
}
