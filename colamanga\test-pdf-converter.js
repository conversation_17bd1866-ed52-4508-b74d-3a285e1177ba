const MangaToPdfConverter = require('./manga-to-pdf');

async function testPdfConverter() {
    console.log('🧪 测试PDF转换器...');
    
    try {
        // 测试构造函数
        const converter = new MangaToPdfConverter();
        console.log('✅ 构造函数成功');
        
        // 测试初始化
        await converter.init();
        console.log('✅ 初始化成功');
        
        // 测试扫描目录
        try {
            const mangaList = await converter.scanMangaDirectory();
            console.log(`✅ 扫描目录成功，找到 ${mangaList.length} 个漫画`);
            
            if (mangaList.length > 0) {
                console.log('📚 漫画列表:');
                mangaList.slice(0, 5).forEach((manga, i) => {
                    console.log(`   ${i + 1}. ${manga.name} (${manga.chapters.length}章)`);
                });
                
                if (mangaList.length > 5) {
                    console.log(`   ... 还有 ${mangaList.length - 5} 个漫画`);
                }
            }
        } catch (error) {
            console.log(`⚠️ 扫描目录失败: ${error.message}`);
        }
        
        // 关闭浏览器
        await converter.close();
        console.log('✅ 测试完成');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
    }
}

testPdfConverter();
