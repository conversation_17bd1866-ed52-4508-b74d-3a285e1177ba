// 测试稳定阈值修改
console.log('🧪 测试稳定阈值修改...');

const fs = require('fs');
const path = require('path');

// 读取文件内容
const filePath = path.join(__dirname, 'download-manga-content.js');
const content = fs.readFileSync(filePath, 'utf8');

// 检查稳定阈值设置
const stableThresholdMatches = content.match(/stableThreshold:\s*(\d+)/g);
const stableThresholdVarMatches = content.match(/const stableThreshold\s*=\s*(\d+)/g);

console.log('📊 稳定阈值检查结果:');

if (stableThresholdMatches) {
    console.log('🔍 配置对象中的稳定阈值:');
    stableThresholdMatches.forEach((match, index) => {
        console.log(`   ${index + 1}. ${match}`);
    });
}

if (stableThresholdVarMatches) {
    console.log('🔍 变量定义中的稳定阈值:');
    stableThresholdVarMatches.forEach((match, index) => {
        console.log(`   ${index + 1}. ${match}`);
    });
}

// 检查是否还有加载率检测
const loadRateMatches = content.match(/loadStatus\.blobRate\s*>=\s*\d+/g);
const loadRateMessages = content.match(/加载率较低/g);

console.log('\n📊 加载率检测检查:');
if (loadRateMatches) {
    console.log('❌ 仍然存在加载率检测:');
    loadRateMatches.forEach((match, index) => {
        console.log(`   ${index + 1}. ${match}`);
    });
} else {
    console.log('✅ 已移除加载率检测');
}

if (loadRateMessages) {
    console.log('❌ 仍然存在加载率相关消息:');
    loadRateMessages.forEach((match, index) => {
        console.log(`   ${index + 1}. ${match}`);
    });
} else {
    console.log('✅ 已移除加载率相关消息');
}

console.log('\n🎯 修改验证完成！');
