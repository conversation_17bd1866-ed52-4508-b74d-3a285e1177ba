{"paramsFile": "params_1.json", "tokenFile": "tokens.json", "outputDir": "E:\\crawlerV2\\output", "requestConfig": {"url": "https://qms.stzy.com/matrix/zw-search/api/v1/homeEs/question/textbookQuery", "timeout": 30000, "retryAttempts": 3, "retryDelay": 1000}, "questionDetail": {"inputDirectory": "E:\\crawlerV2\\output\\初中\\数学\\人教版(2024)\\七年级下\\第七章 相交线与平行线 - 副本", "outputDirectory": "E:\\zhongwang_xkw"}, "tokenRotation": {"intervalMinutes": 5, "maxUsagePerToken": 100}, "concurrency": {"maxConcurrent": 13, "delayBetweenRequests": {"min": 6000, "max": 11000}, "delayBetweenConcurrentTasks": {"min": 5000, "max": 10000}}, "proxy": {"enabled": true, "host": "e369.kdltps.com", "port": 15818, "username": "t15316677787531", "password": "4sjsw6br"}, "userAgents": ["Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:132.0) Gecko/20100101 Firefox/132.0", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:132.0) Gecko/20100101 Firefox/132.0"], "headers": {"Accept": "application/json, text/plain, */*", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Connection": "keep-alive", "Content-Type": "application/json", "DNT": "1", "Host": "qms.stzy.com", "Origin": "https://zj.stzy.com", "Referer": "https://zj.stzy.com/", "Sec-Fetch-Dest": "empty", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Site": "same-site"}, "secChUa": ["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "\"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\", \"Not=A?Brand\";v=\"99\"", "\"Google Chrome\";v=\"137\", \"Not;A=Brand\";v=\"8\", \"Chromium\";v=\"137\"", "\"Firefox\";v=\"132\", \"Not;A=Brand\";v=\"99\"", "\"Mozilla\";v=\"5.0\", \"Firefox\";v=\"132\""], "platforms": ["macOS", "Windows", "Linux"]}