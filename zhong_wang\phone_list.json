{"phones": [{"phone": "19269465816", "tokenId": 10, "registeredAt": "2025-07-25T01:51:53.173Z", "tokenValid": true, "lastTokenTest": "2025-07-28T08:57:56.430Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19219302169", "tokenId": 11, "registeredAt": "2025-07-25T01:52:18.623Z", "tokenValid": true, "lastTokenTest": "2025-07-28T08:57:57.265Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "17042059283", "tokenId": 12, "registeredAt": "2025-07-25T01:52:44.393Z", "tokenValid": true, "lastTokenTest": "2025-07-28T08:57:58.040Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19269442712", "tokenId": 13, "registeredAt": "2025-07-25T01:53:06.362Z", "tokenValid": true, "lastTokenTest": "2025-07-28T08:57:58.872Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19219392409", "tokenId": 5, "registeredAt": "2025-07-25T08:30:31.987Z", "tokenValid": true, "lastTokenTest": "2025-07-28T08:57:59.754Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19216896539", "tokenId": 6, "registeredAt": "2025-07-25T08:31:02.564Z", "tokenValid": true, "lastTokenTest": "2025-07-28T08:58:00.551Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19297003462", "tokenId": 7, "registeredAt": "2025-07-25T09:11:04.413Z", "tokenValid": true, "lastTokenTest": "2025-07-28T08:58:01.391Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19237259604", "tokenId": 1, "registeredAt": "2025-07-28T02:46:27.007Z", "tokenValid": true, "lastTokenTest": "2025-07-28T08:58:02.216Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19299400443", "tokenId": 2, "registeredAt": "2025-07-28T02:47:03.852Z", "tokenValid": true, "lastTokenTest": "2025-07-28T08:58:03.050Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19212758604", "tokenId": 3, "registeredAt": "2025-07-28T02:47:33.147Z", "tokenValid": true, "lastTokenTest": "2025-07-28T08:58:03.848Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "17030306559", "tokenId": 4, "registeredAt": "2025-07-28T02:48:02.274Z", "tokenValid": true, "lastTokenTest": "2025-07-28T08:58:04.661Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19224094914", "tokenId": 5, "registeredAt": "2025-07-28T02:48:31.442Z", "tokenValid": true, "lastTokenTest": "2025-07-28T08:58:05.604Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19267275962", "tokenId": 6, "registeredAt": "2025-07-28T02:49:05.262Z", "tokenValid": true, "lastTokenTest": "2025-07-28T08:58:06.554Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19315287941", "tokenId": 7, "registeredAt": "2025-07-28T02:49:31.509Z", "tokenValid": true, "lastTokenTest": "2025-07-28T08:58:07.459Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19277283794", "tokenId": 8, "registeredAt": "2025-07-28T02:49:57.135Z", "tokenValid": true, "lastTokenTest": "2025-07-28T08:58:08.234Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19269400238", "tokenId": 9, "registeredAt": "2025-07-28T02:50:28.548Z", "tokenValid": true, "lastTokenTest": "2025-07-28T08:58:09.261Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19224094746", "tokenId": 10, "registeredAt": "2025-07-28T02:50:58.001Z", "tokenValid": true, "lastTokenTest": "2025-07-28T08:58:10.254Z"}], "lastUpdated": "2025-07-28T08:58:10.254Z", "totalCount": 17, "description": "已注册的手机号列表"}